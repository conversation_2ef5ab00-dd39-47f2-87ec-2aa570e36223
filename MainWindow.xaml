<Window x:Class="BoardDesigner.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:behaviors="clr-namespace:BoardDesigner.Behaviors"
        xmlns:viewmodels="clr-namespace:BoardDesigner.ViewModels"
        xmlns:models="clr-namespace:BoardDesigner.Models"
        Title="设备看板设计器" Height="800" Width="1200"
        WindowState="Maximized">

    <Window.DataContext>
        <viewmodels:MainViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <ToolBar Grid.Row="0" Background="LightGray">
            <Button Content="新建" Padding="10,5"/>
            <Button Content="打开" Padding="10,5"/>
            <Button Content="保存" Padding="10,5"/>
            <Separator/>
            <Button Content="删除选中" Command="{Binding DeleteComponentCommand}" Padding="10,5"/>
            <Button Content="清空画布" Command="{Binding ClearCanvasCommand}" Padding="10,5"/>
            <Separator/>
            <Button Content="网格对齐" Padding="10,5"/>
            <Button Content="显示网格" Padding="10,5"/>
        </ToolBar>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧工具箱 -->
            <Border Grid.Column="0" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="设备工具箱" FontWeight="Bold"
                              Margin="10" HorizontalAlignment="Center"/>

                    <ListBox Grid.Row="1" ItemsSource="{Binding ToolboxComponents}"
                            Background="Transparent" BorderThickness="0"
                            ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <i:Interaction.Behaviors>
                            <behaviors:ToolboxDragBehavior/>
                        </i:Interaction.Behaviors>

                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White" BorderBrush="LightGray"
                                       BorderThickness="1" Margin="5" Padding="10"
                                       CornerRadius="3" Cursor="Hand">
                                    <StackPanel>
                                        <ContentPresenter Content="{Binding}"
                                                        RenderTransform="0.7,0,0,0.7,0,0"/>
                                        <TextBlock Text="{Binding DisplayName}"
                                                  HorizontalAlignment="Center"
                                                  FontSize="10" Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" Width="5" Background="Gray"
                         HorizontalAlignment="Stretch"/>

            <!-- 中间画布区域 -->
            <Border Grid.Column="2" Background="White" BorderBrush="Gray" BorderThickness="1">
                <ScrollViewer HorizontalScrollBarVisibility="Auto"
                             VerticalScrollBarVisibility="Auto">
                    <Canvas Width="2000" Height="1500"
                           ClipToBounds="True">
                        <i:Interaction.Behaviors>
                            <behaviors:CanvasDropBehavior/>
                        </i:Interaction.Behaviors>

                        <!-- 网格背景 -->
                        <Canvas.Background>
                            <DrawingBrush TileMode="Tile" Viewport="0,0,20,20" ViewportUnits="Absolute">
                                <DrawingBrush.Drawing>
                                    <GeometryDrawing>
                                        <GeometryDrawing.Geometry>
                                            <RectangleGeometry Rect="0,0,20,20"/>
                                        </GeometryDrawing.Geometry>
                                        <GeometryDrawing.Pen>
                                            <Pen Brush="LightGray" Thickness="0.5"/>
                                        </GeometryDrawing.Pen>
                                    </GeometryDrawing>
                                </DrawingBrush.Drawing>
                            </DrawingBrush>
                        </Canvas.Background>

                        <!-- 画布组件 -->
                        <ItemsControl ItemsSource="{Binding CanvasComponents}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <Canvas/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>

                            <ItemsControl.ItemContainerStyle>
                                <Style TargetType="ContentPresenter">
                                    <Setter Property="Canvas.Left" Value="{Binding X}"/>
                                    <Setter Property="Canvas.Top" Value="{Binding Y}"/>
                                    <EventSetter Event="MouseLeftButtonDown" Handler="Component_MouseLeftButtonDown"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                            <Setter Property="Effect">
                                                <Setter.Value>
                                                    <DropShadowEffect Color="Blue" ShadowDepth="0" BlurRadius="8"/>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ItemsControl.ItemContainerStyle>

                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding}">
                                        <i:Interaction.Behaviors>
                                            <behaviors:DragDropBehavior/>
                                        </i:Interaction.Behaviors>
                                    </ContentPresenter>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </Canvas>
                </ScrollViewer>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" Width="5" Background="Gray"
                         HorizontalAlignment="Stretch"/>

            <!-- 右侧属性面板 -->
            <Border Grid.Column="4" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="属性面板" FontWeight="Bold"
                              Margin="10" HorizontalAlignment="Center"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ContentPresenter Content="{Binding SelectedComponent}"
                                        Margin="10">
                            <ContentPresenter.Resources>
                                <!-- 设备看板属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:DeviceBoard}">
                                    <StackPanel>
                                        <TextBlock Text="设备看板属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <TextBlock Text="基本属性" FontWeight="Bold" Margin="0,5"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>
                                        </Grid>

                                        <TextBlock Text="设备属性" FontWeight="Bold" Margin="0,15,0,5"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding DeviceName}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="设备类型:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DeviceType}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="位置:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Location}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="批次号:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding BatchNumber}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="温度:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Temperature}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="湿度:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding Humidity}" Margin="5"/>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="状态:" Margin="0,5"/>
                                            <ComboBox Grid.Row="6" Grid.Column="1" SelectedItem="{Binding Status}" Margin="5">
                                                <ComboBox.Items>
                                                    <models:DeviceStatus>Normal</models:DeviceStatus>
                                                    <models:DeviceStatus>Warning</models:DeviceStatus>
                                                    <models:DeviceStatus>Error</models:DeviceStatus>
                                                    <models:DeviceStatus>Offline</models:DeviceStatus>
                                                </ComboBox.Items>
                                            </ComboBox>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 墙体属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:Wall}">
                                    <StackPanel>
                                        <TextBlock Text="墙体属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="边框厚度:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding BorderThickness}" Margin="5"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 区域属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:Area}">
                                    <StackPanel>
                                        <TextBlock Text="区域属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="区域名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding AreaName}" Margin="5"/>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="描述:" Margin="0,5"/>
                                            <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding Description}" Margin="5" AcceptsReturn="True" Height="60"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>

                                <!-- 报警灯属性编辑模板 -->
                                <DataTemplate DataType="{x:Type models:AlarmLight}">
                                    <StackPanel>
                                        <TextBlock Text="报警灯属性" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                                <RowDefinition/>
                                            </Grid.RowDefinitions>

                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="X:" Margin="0,5"/>
                                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding X}" Margin="5"/>

                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Y:" Margin="0,5"/>
                                            <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Y}" Margin="5"/>

                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="宽度:" Margin="0,5"/>
                                            <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Width}" Margin="5"/>

                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="高度:" Margin="0,5"/>
                                            <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding Height}" Margin="5"/>

                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="名称:" Margin="0,5"/>
                                            <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Name}" Margin="5"/>

                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="报警状态:" Margin="0,5"/>
                                            <ComboBox Grid.Row="5" Grid.Column="1" SelectedItem="{Binding AlarmStatus}" Margin="5">
                                                <ComboBox.Items>
                                                    <models:AlarmStatus>Off</models:AlarmStatus>
                                                    <models:AlarmStatus>Normal</models:AlarmStatus>
                                                    <models:AlarmStatus>Warning</models:AlarmStatus>
                                                    <models:AlarmStatus>Alarm</models:AlarmStatus>
                                                </ComboBox.Items>
                                            </ComboBox>

                                            <TextBlock Grid.Row="6" Grid.Column="0" Text="报警信息:" Margin="0,5"/>
                                            <TextBox Grid.Row="6" Grid.Column="1" Text="{Binding AlarmMessage}" Margin="5"/>
                                        </Grid>
                                    </StackPanel>
                                </DataTemplate>
                            </ContentPresenter.Resources>
                        </ContentPresenter>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding CanvasComponents.Count, StringFormat='组件数量: {0}'}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding SelectedComponent.DisplayName, StringFormat='选中: {0}'}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
