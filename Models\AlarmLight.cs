using System.Windows.Media;

namespace BoardDesigner.Models
{
    public enum AlarmStatus
    {
        Off,      // 关闭 - 灰色
        Normal,   // 正常 - 绿色
        Warning,  // 警告 - 黄色
        Alarm     // 报警 - 红色闪烁
    }

    public class AlarmLight : ComponentBase
    {
        private AlarmStatus _alarmStatus = AlarmStatus.Off;
        private bool _isBlinking = false;
        private string _alarmMessage = "";

        public override string ComponentType => "AlarmLight";
        public override string DisplayName => "报警灯";

        public AlarmStatus AlarmStatus
        {
            get => _alarmStatus;
            set
            {
                if (SetProperty(ref _alarmStatus, value))
                {
                    OnPropertyChanged(nameof(LightColor));
                }
            }
        }

        public bool IsBlinking
        {
            get => _isBlinking;
            set => SetProperty(ref _isBlinking, value);
        }

        public string AlarmMessage
        {
            get => _alarmMessage;
            set => SetProperty(ref _alarmMessage, value);
        }

        public Brush LightColor
        {
            get
            {
                return AlarmStatus switch
                {
                    AlarmStatus.Off => Brushes.Gray,
                    AlarmStatus.Normal => Brushes.Green,
                    AlarmStatus.Warning => Brushes.Yellow,
                    AlarmStatus.Alarm => Brushes.Red,
                    _ => Brushes.Gray
                };
            }
        }

        public AlarmLight()
        {
            Width = 60;
            Height = 60;
            Name = "报警灯";
        }
    }
}
