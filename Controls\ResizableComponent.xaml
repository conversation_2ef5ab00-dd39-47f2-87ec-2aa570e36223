<UserControl x:Class="BoardDesigner.Controls.ResizableComponent"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:behaviors="clr-namespace:BoardDesigner.Behaviors">

    <Grid>
        <!-- 主要内容 -->
        <ContentPresenter x:Name="ContentPresenter" Content="{Binding}"/>

        <!-- 选中状态边框和调整手柄 -->
        <Border x:Name="SelectionBorder"
                BorderBrush="#2196F3"
                BorderThickness="2"
                Visibility="{Binding IsSelected, Converter={StaticResource BooleanToVisibilityConverter}}"
                Background="Transparent">

            <Grid>
                <!-- 调整手柄 -->
                <!-- 左上角 -->
                <Thumb x:Name="TopLeftThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Left"
                       VerticalAlignment="Top"
                       Margin="-4,-4,0,0"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNWSE"/>

                <!-- 右上角 -->
                <Thumb x:Name="TopRightThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Top"
                       Margin="0,-4,-4,0"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNESW"/>

                <!-- 左下角 -->
                <Thumb x:Name="BottomLeftThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Left"
                       VerticalAlignment="Bottom"
                       Margin="-4,0,0,-4"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNESW"/>

                <!-- 右下角 -->
                <Thumb x:Name="BottomRightThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Bottom"
                       Margin="0,0,-4,-4"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNWSE"/>

                <!-- 上边中点 -->
                <Thumb x:Name="TopThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Top"
                       Margin="0,-4,0,0"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNS"/>

                <!-- 下边中点 -->
                <Thumb x:Name="BottomThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Bottom"
                       Margin="0,0,0,-4"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeNS"/>

                <!-- 左边中点 -->
                <Thumb x:Name="LeftThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Left"
                       VerticalAlignment="Center"
                       Margin="-4,0,0,0"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeWE"/>

                <!-- 右边中点 -->
                <Thumb x:Name="RightThumb"
                       Width="8" Height="8"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Center"
                       Margin="0,0,-4,0"
                       Background="#2196F3"
                       BorderBrush="White"
                       BorderThickness="1"
                       Cursor="SizeWE"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
