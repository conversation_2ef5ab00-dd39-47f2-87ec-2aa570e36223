# 设备看板设计器 (BoardDesigner)

一个基于WPF的设备管理设计器应用程序，允许用户通过拖拽方式设计设备布局图。

## 功能特性

### 🎯 核心功能
- **左侧工具箱**: 包含可拖拽的设备组件
- **中间画布**: 支持拖拽布局的设计区域
- **右侧属性面板**: 实时编辑选中组件的属性

### 🔧 支持的组件类型

#### 1. 设备看板
- 显示设备状态（正常/警告/错误/离线）
- 设备属性（名称、类型、位置）
- 实时数据（温度、湿度）
- 批次信息
- 状态颜色指示

#### 2. 墙体
- 长方形墙体组件
- 可调整尺寸和边框样式
- 支持自定义颜色

#### 3. 区域
- 半透明区域标识
- 可添加区域名称和描述
- 支持自定义颜色和边框

#### 4. 报警灯
- 圆形报警指示器
- 多种状态（关闭/正常/警告/报警）
- 状态颜色变化
- 支持报警信息

## 🚀 使用方法

### 启动应用程序
```bash
dotnet run
```

### 基本操作
1. **添加组件**:
   - 点击左侧工具箱中的组件按钮
   - 或从工具箱拖拽组件到中间画布
2. **选择组件**: 点击画布上的组件进行选择（显示蓝色边框和调整手柄）
3. **移动组件**: 拖拽选中的组件到新位置
4. **调整大小**: 拖拽选中组件边框上的8个调整手柄来改变大小
5. **编辑属性**: 在右侧属性面板修改组件属性
6. **删除组件**: 选中组件后点击"删除"按钮或按Delete键

### 🎯 完整功能列表

#### 📁 **文件操作**
- **新建项目** (Ctrl+N): 创建新的设计项目
- **打开项目** (Ctrl+O): 打开已保存的项目文件
- **保存项目** (Ctrl+S): 保存当前设计到文件

#### ✂️ **编辑操作**
- **复制组件** (Ctrl+C): 复制选中的组件
- **粘贴组件** (Ctrl+V): 粘贴已复制的组件
- **删除组件** (Delete): 删除选中的组件
- **清空画布**: 清除所有组件

#### 🎨 **视图操作**
- **网格对齐** (Ctrl+G): 将选中组件对齐到20px网格
- **显示网格** (G): 切换网格显示/隐藏
- **可视化大小调整**: 选中组件后显示8个调整手柄
- **实时选中反馈**: 蓝色边框和调整手柄

#### 🎯 **组件功能**
- **现代化工具箱**: Expander分组、图标化按钮、拖拽支持
- **美化组件外观**: 全新设计的组件样式
- **精确位置控制**: 像素级精确的位置和大小调整

### ⌨️ **键盘快捷键**
| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建项目 |
| Ctrl+O | 打开项目 |
| Ctrl+S | 保存项目 |
| Ctrl+C | 复制组件 |
| Ctrl+V | 粘贴组件 |
| Delete | 删除选中组件 |
| G | 切换网格显示 |
| Ctrl+G | 网格对齐 |

## 🏗️ 项目结构

```
BoardDesigner/
├── Models/                 # 数据模型
│   ├── ComponentBase.cs   # 组件基类
│   ├── DeviceBoard.cs     # 设备看板模型
│   ├── Wall.cs            # 墙体模型
│   ├── Area.cs            # 区域模型
│   └── AlarmLight.cs      # 报警灯模型
├── ViewModels/            # 视图模型
│   └── MainViewModel.cs   # 主视图模型
├── Controls/              # 自定义控件
│   ├── ResizableComponent.xaml    # 可调整大小的组件容器
│   └── ResizableComponent.xaml.cs # 容器逻辑代码
├── Behaviors/             # 行为类
│   └── DragDropBehavior.cs # 拖拽行为
├── Styles/                # 样式资源
│   └── ComponentStyles.xaml # 组件样式
├── MainWindow.xaml        # 主窗口界面
├── MainWindow.xaml.cs     # 主窗口代码
├── App.xaml              # 应用程序资源
└── BoardDesigner.csproj  # 项目文件
```

## 🎨 界面布局

### 左侧工具箱 (200px)
- **现代化分组设计**: 使用Expander控件分组显示
- **设备组件**: 设备看板（带图标和预览）
- **结构组件**: 墙体、区域（图标化显示）
- **指示组件**: 报警灯（发光效果预览）
- **一键添加**: 点击按钮直接添加组件到画布

### 中间画布 (自适应)
- 2000x1500像素设计区域
- 20x20像素网格背景
- 支持滚动和缩放
- 拖拽放置区域

### 右侧属性面板 (250px)
- 基本属性编辑（位置、尺寸、名称）
- 组件特定属性编辑
- 实时属性更新

## 🔧 技术栈

- **.NET 6.0**: 目标框架
- **WPF**: 用户界面框架
- **MVVM**: 架构模式
- **Microsoft.Xaml.Behaviors**: 行为支持
- **Data Binding**: 数据绑定
- **Drag & Drop**: 拖拽功能

## 📋 系统要求

- Windows 10/11
- .NET 6.0 Runtime
- Visual Studio 2022 (开发环境)

## 🚧 开发计划

### 已完成功能
- ✅ 基本拖拽功能
- ✅ 组件选择和属性编辑
- ✅ 四种基本组件类型（设备看板、墙体、区域、报警灯）
- ✅ 状态颜色指示
- ✅ 网格背景显示
- ✅ **可视化大小调整** - 8个调整手柄支持
- ✅ **美化组件外观** - 现代化设计风格
- ✅ **实时选中反馈** - 蓝色边框和调整手柄
- ✅ **精确位置控制** - 像素级精度
- ✅ **现代化工具箱** - Expander分组、图标化按钮、拖拽支持
- ✅ **完整菜单栏** - 文件操作、编辑操作、视图操作
- ✅ **键盘快捷键** - 全套快捷键支持
- ✅ **组件复制粘贴** - 支持组件的复制和粘贴
- ✅ **网格对齐功能** - 20px网格对齐
- ✅ **网格显示切换** - 可切换网格显示/隐藏

### 待开发功能
- 🔄 文件保存和加载（JSON序列化）
- 🔄 撤销/重做功能
- 🔄 组件分组
- 🔄 画布缩放功能
- 🔄 导出为图片
- 🔄 组件模板库
- 🔄 多页面支持

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**注意**: 这是一个演示项目，展示了WPF中拖拽设计器的实现方法。
