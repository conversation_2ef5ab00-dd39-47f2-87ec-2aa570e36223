<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:models="clr-namespace:BoardDesigner.Models">

    <!-- 设备看板样式 -->
    <DataTemplate DataType="{x:Type models:DeviceBoard}">
        <Border Background="White" 
                BorderBrush="{Binding StatusColor}" 
                BorderThickness="3"
                CornerRadius="5"
                Width="{Binding Width}"
                Height="{Binding Height}">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.3"/>
            </Border.Effect>
            <Grid Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 设备名称 -->
                <TextBlock Grid.Row="0" Text="{Binding DeviceName}" 
                          FontWeight="Bold" FontSize="12" 
                          HorizontalAlignment="Center"
                          Foreground="{Binding StatusColor}"/>
                
                <!-- 设备信息 -->
                <StackPanel Grid.Row="1" Margin="0,5">
                    <TextBlock Text="{Binding DeviceType}" FontSize="10" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding Location}" FontSize="10" HorizontalAlignment="Center"/>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="温度: " FontSize="9"/>
                        <TextBlock Text="{Binding Temperature}" FontSize="9"/>
                        <TextBlock Text="°C" FontSize="9"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="湿度: " FontSize="9"/>
                        <TextBlock Text="{Binding Humidity}" FontSize="9"/>
                        <TextBlock Text="%" FontSize="9"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- 批次信息 -->
                <TextBlock Grid.Row="2" Text="{Binding BatchNumber}" 
                          FontSize="9" HorizontalAlignment="Center"
                          Background="LightGray" Padding="2"/>
            </Grid>
        </Border>
    </DataTemplate>

    <!-- 墙体样式 -->
    <DataTemplate DataType="{x:Type models:Wall}">
        <Rectangle Fill="{Binding FillColor}"
                   Stroke="{Binding BorderColor}"
                   StrokeThickness="{Binding BorderThickness}"
                   Width="{Binding Width}"
                   Height="{Binding Height}"/>
    </DataTemplate>

    <!-- 区域样式 -->
    <DataTemplate DataType="{x:Type models:Area}">
        <Border Background="{Binding FillColor}"
                BorderBrush="{Binding BorderColor}"
                BorderThickness="{Binding BorderThickness}"
                Width="{Binding Width}"
                Height="{Binding Height}"
                CornerRadius="5">
            <TextBlock Text="{Binding AreaName}" 
                      HorizontalAlignment="Center" 
                      VerticalAlignment="Top"
                      Margin="5"
                      FontWeight="Bold"
                      Foreground="{Binding BorderColor}"/>
        </Border>
    </DataTemplate>

    <!-- 报警灯样式 -->
    <DataTemplate DataType="{x:Type models:AlarmLight}">
        <Grid Width="{Binding Width}" Height="{Binding Height}">
            <Ellipse Fill="{Binding LightColor}"
                     Stroke="Black"
                     StrokeThickness="2">
                <Ellipse.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.5"/>
                </Ellipse.Effect>
            </Ellipse>
            <Ellipse Fill="{Binding LightColor}"
                     Width="30" Height="30"
                     HorizontalAlignment="Center"
                     VerticalAlignment="Center"
                     Opacity="0.7"/>
        </Grid>
    </DataTemplate>

    <!-- 选中状态样式 -->
    <Style x:Key="SelectedComponentStyle" TargetType="ContentPresenter">
        <Style.Triggers>
            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Blue" ShadowDepth="0" BlurRadius="10"/>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
