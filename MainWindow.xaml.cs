using System.Windows;
using System.Windows.Input;
using BoardDesigner.ViewModels;
using BoardDesigner.Models;

namespace BoardDesigner
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void Component_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element && element.DataContext is ComponentBase component)
            {
                if (DataContext is MainViewModel viewModel)
                {
                    viewModel.SelectedComponent = component;
                }
            }
        }
    }
}
