using System.Windows;
using System.Windows.Controls;
using BoardDesigner.ViewModels;
using BoardDesigner.Models;

namespace BoardDesigner
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void ToolboxButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ComponentBase template)
            {
                if (DataContext is MainViewModel viewModel)
                {
                    viewModel.AddComponentCommand.Execute(template);
                }
            }
        }
    }
}
