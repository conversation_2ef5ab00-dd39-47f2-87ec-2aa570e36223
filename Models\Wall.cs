using System.Windows.Media;

namespace BoardDesigner.Models
{
    public class Wall : ComponentBase
    {
        private Brush _fillColor = Brushes.LightGray;
        private Brush _borderColor = Brushes.Black;
        private double _borderThickness = 2;

        public override string ComponentType => "Wall";
        public override string DisplayName => "墙体";

        public Brush FillColor
        {
            get => _fillColor;
            set => SetProperty(ref _fillColor, value);
        }

        public Brush BorderColor
        {
            get => _borderColor;
            set => SetProperty(ref _borderColor, value);
        }

        public double BorderThickness
        {
            get => _borderThickness;
            set => SetProperty(ref _borderThickness, value);
        }

        public Wall()
        {
            Width = 200;
            Height = 20;
            Name = "墙体";
        }
    }
}
