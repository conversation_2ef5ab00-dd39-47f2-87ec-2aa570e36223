using System.Windows.Media;

namespace BoardDesigner.Models
{
    public class Area : ComponentBase
    {
        private string _areaName = "区域";
        private Brush _fillColor = new SolidColorBrush(Color.FromArgb(50, 0, 100, 200));
        private Brush _borderColor = Brushes.Blue;
        private double _borderThickness = 2;
        private string _description = "";

        public override string ComponentType => "Area";
        public override string DisplayName => "区域";

        public string AreaName
        {
            get => _areaName;
            set => SetProperty(ref _areaName, value);
        }

        public Brush FillColor
        {
            get => _fillColor;
            set => SetProperty(ref _fillColor, value);
        }

        public Brush BorderColor
        {
            get => _borderColor;
            set => SetProperty(ref _borderColor, value);
        }

        public double BorderThickness
        {
            get => _borderThickness;
            set => SetProperty(ref _borderThickness, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public Area()
        {
            Width = 200;
            Height = 150;
            Name = "区域";
        }
    }
}
